<template>
	<CustomDrawer
		v-model:show="show"
		title="Konfirmasi"
		:background="agenda?.greeting_background"
		@close="handleClose"
		@after-enter="handleAfterEnter"
	>
		<div class="text-center">
			<n-gradient-text class="text-pretty" :size="24" type="primary">{{ agenda.title }}</n-gradient-text>
			<n-h4 class="mb-0 mt-2">SELAMAT DATANG</n-h4>
			<n-h4 class="mb-0 mt-2 text-pretty">{{ selectedGuest?.name }}</n-h4>
			<n-text class="text-pretty" depth="3">{{ selectedGuest?.status_relation ?? "-" }}</n-text>
			<br />
			<n-tag v-if="selectedGuest?.level === 3" round type="warning">VVIP</n-tag>
			<n-tag v-if="selectedGuest?.level === 2" round type="warning">VIP</n-tag>
			<n-tag v-if="selectedGuest?.level === 1" round>Reguler</n-tag>
			<br />
			<n-text depth="3">{{ selectedGuest?.table ?? "-" }}</n-text>
			<!-- <br /> -->
			<n-divider dashed>
				<n-text depth="3">{{ selectedGuest?.code_guest }}</n-text>
			</n-divider>
			<div v-if="selectedGuest?.note" class="w-auto border-2 border-dashed border-[#fc7ed1] p-2 mb-4">
				{{ selectedGuest?.note }}
			</div>
		</div>

		<n-form ref="formGuestRef" :model="guestModel" @keydown.enter.prevent="handleSubmit">
			<n-form-item label="Jumlah tamu" path="amount_guest">
				<n-input-number
					v-model:value="guestModel.amount_guest"
					clearable
					:min="0"
					style="width: 100%"
					placeholder="Masukkan jumlah tamu"
				/>
			</n-form-item>
			<n-form-item label="Hadiah" path="gift">
				<n-checkbox-group v-model:value="guestModel.gift">
					<n-space item-style="display: flex;">
						<n-checkbox value="1" label="Cash" />
						<n-checkbox value="2" label="Kado" />
						<n-checkbox value="3" label="Transfer" />
					</n-space>
				</n-checkbox-group>
			</n-form-item>
			<n-form-item label="Label Hadiah" path="label">
				<n-input
					v-model:value="guestModel.label"
					type="text"
					clearable
					placeholder="Masukkan label hadiah (opsional)"
				/>
			</n-form-item>
			<n-form-item label="Jumlah souvenir" path="amount_souvenir">
				<n-input-number
					v-model:value="guestModel.amount_souvenir"
					clearable
					:min="0"
					style="width: 100%"
					placeholder="Masukkan jumlah souvenir"
				/>
			</n-form-item>
			<n-form-item label="Souvenir diberikan" path="take_souvenir">
				<n-switch v-model:value="guestModel.take_souvenir" />
			</n-form-item>

			<n-form-item v-if="agenda?.photo">
				<SelfieInput
					v-model="guestModel.selfieInput"
					camera-title="Selfie"
					camera-button-text="Ambil Selfie"
					aspect-ratio="1/1"
				/>
			</n-form-item>

			<template v-for="(item, index) in entrustModel.entrusts" :key="'titipan-' + index">
				<n-card class="mb-3" :title="'Titipan ' + (index + 1)" closable @close="removeItem(index)">
					<n-form-item ignore-path-change label="Nama" :path="`entrusts.${index}.name`">
						<n-input
							v-model:value="item.name"
							placeholder="Masukkan nama tamu"
							@keydown.enter.prevent="handleSubmit"
						/>
					</n-form-item>
					<n-form-item ignore-path-change label="Hadiah" :path="`entrusts.${index}.gift`">
						<n-checkbox-group v-model:value="item.gift">
							<n-space item-style="display: flex;">
								<n-checkbox value="1" label="Cash" />
								<n-checkbox value="2" label="Kado" />
								<n-checkbox value="3" label="Transfer" />
							</n-space>
						</n-checkbox-group>
					</n-form-item>
					<n-form-item label="Souvenir diberikan">
						<n-switch v-model:value="item.take_souvenir" />
					</n-form-item>
					<n-form-item v-if="agenda?.print_feature && !isIos">
						<n-button class="w-full mb-4" @click="printEntrust(index)">
							<template #icon>
								<Icon name="carbon:printer" />
							</template>
							Cetak Titipan
						</n-button>
					</n-form-item>
				</n-card>
			</template>

			<n-form-item>
				<n-button block @click="addEntrust">
					<template #icon>
						<Icon name="carbon:gift" />
					</template>
					Tambah titipan
				</n-button>
			</n-form-item>
			<n-button v-if="agenda?.print_feature && !isIos" class="w-full mb-4" @click="printContent">
				<template #icon>
					<Icon name="carbon:printer" />
				</template>
				Cetak
			</n-button>
		</n-form>

		<template #footer>
			<n-grid :x-gap="4" :cols="2">
				<n-grid-item>
					<n-button
						v-if="props.selectedGuest.presence"
						block
						:loading="cancelLoading"
						type="error"
						style="z-index: 100"
						@click="cancelAttendance"
					>
						Batalkan
					</n-button>
				</n-grid-item>
				<n-grid-item>
					<n-button block :loading="submitLoading" type="primary" style="z-index: 100" @click="handleSubmit">
						Konfirmasi
					</n-button>
				</n-grid-item>
			</n-grid>
		</template>
	</CustomDrawer>
</template>

<script setup>
import {
	NGrid,
	NGridItem,
	NTag,
	NGradientText,
	NText,
	NH4,
	NDivider,
	NCard,
	NForm,
	NFormItem,
	NInputNumber,
	NCheckboxGroup,
	NSpace,
	NCheckbox,
	NInput,
	NSwitch,
	NButton,
	useLoadingBar,
	useNotification,
	useMessage
} from "naive-ui"
import CustomDrawer from "@/components/app/CustomDrawer.vue"
import ShortUniqueId from "short-unique-id"
// import dayjs from "dayjs"

const props = defineProps({
	agenda: Object,
	selectedGuest: Object
})

const emit = defineEmits(["close", "after-submit", "print-content", "print-entrust"])

const show = defineModel()
const { randomUUID } = new ShortUniqueId({ length: 4 })

const loadingBar = useLoadingBar()
const notification = useNotification()
const message = useMessage()
const user = useDirectusUser()
const { updateItem, createItems } = useDirectusItems()
const selectedPubChannel = useSelectedPubChannel()
const ably = useNuxtApp().$ably
const formGuestRef = ref(null)
const { isIos } = useDevice()
const config = useRuntimeConfig()
const { token } = useDirectusToken()

const guestModel = ref({
	amount_guest: 0,
	amount_souvenir: 0,
	take_souvenir: false,
	gift: [],
	label: null,
	regist_by: user.value.id,
	selfie: null,
	selfieInput: null
})

const entrustModel = reactive({
	entrusts: []
})

const resetEntrust = () => {
	entrustModel.entrusts = []
}

const sendMessage = message => {
	let channelName = `${props.agenda.id}`
	if (selectedPubChannel.value != "global") {
		channelName = `${props.agenda.id}-${user.value.id}`
	}
	const channel = ably.channels.get(channelName)
	channel.publish("greeting", message)
}

const addEntrust = () => {
	entrustModel.entrusts.push({
		name: null,
		code_guest: null,
		gift: [],
		presence: false,
		amount_guest: 1,
		take_souvenir: false,
		amount_souvenir: 1,
		entrust: true,
		event: "props.agenda.id",
		regist_by: user.value.id,
		entrust_by: props.selectedGuest.name
	})
}

const removeItem = index => {
	entrustModel.entrusts.splice(index, 1)
}

const submitLoading = ref(false)

const handleSubmit = e => {
	e.preventDefault()
	formGuestRef.value?.validate(async errors => {
		if (!errors) {
			await submitData()
		} else {
			console.error(errors)
		}
	})
}

const cancelLoading = ref(false)
const cancelAttendance = async e => {
	e.preventDefault()
	cancelLoading.value = true
	loadingBar.start()
	try {
		await updateItem({ id: props.selectedGuest.id, collection: "guest", item: { presence: false } })
		emit("after-submit")
		notification.success({
			content: `Kehadiran ${props.selectedGuest.name} dibatalkan`,
			description: `Sukses`,
			duration: 2000
		})
	} catch (e) {
		console.error(e)
		message.error("gagal submit")
	} finally {
		cancelLoading.value = false
		loadingBar.finish()
	}
}

function safeFileName(name) {
	return name
		.toLowerCase()
		.replace(/[^\w\s.-]/g, "") // remove any character except word characters, spaces, dots, and dashes
		.replace(/\s+/g, "_") // replace spaces with underscores
		.replace(/_+/g, "_") // collapse multiple underscores
		.replace(/^_+|_+$/g, "") // remove underscores at start/end
}

const submitData = async () => {
	submitLoading.value = true
	loadingBar.start()
	try {
		if (props.agenda.photo && guestModel.value.selfieInput) {
			const nameSafe = safeFileName(props.selectedGuest.name)
			const base64Response = await fetch(guestModel.value.selfieInput)
			const blob = await base64Response.blob()
			// Upload the file and assign it to the folder
			const formData = new FormData()
			formData.append("folder", "4327512e-0b84-4b2f-b844-1f5929e8b9d9") // Assign file to folder
			formData.append("file", blob, `${nameSafe}--${props.selectedGuest.code_guest}.jpeg`)

			const result = await $fetch(`/files`, {
				baseURL: config.public.directusUrl,
				method: "POST",
				headers: {
					Authorization: `Bearer ${token.value}`
				},
				body: formData
			})
			guestModel.value.selfie = result.data.id
		}

		guestModel.value.presence = true
		guestModel.value.attendance_time = dayjs().utcOffset(7).format("YYYY-MM-DD HH:mm:ss")
		const body = {
			...props.selectedGuest,
			...guestModel.value
		}
		// console.log("attend", body.attendance_time)
		await updateItem({ id: props.selectedGuest.id, collection: "guest", item: body })
		// console.log("result", result)

		if (entrustModel.entrusts.length > 0) {
			entrustModel.entrusts.forEach((item, index) => {
				if (!item.name) {
					entrustModel.entrusts.splice(index, 1)
				}
			})
			entrustModel.entrusts.forEach(item => {
				item.event = props.agenda.id
				item.code_guest = props.agenda.code_event + "-gift-" + randomUUID()
			})
		}

		// console.log("entrustModel.entrusts", entrustModel.entrusts)
		const guestMessage = {
			// id: props.selectedGuest.id,
			name: props.selectedGuest.name,
			status_relation: props.selectedGuest.status_relation,
			table: props.selectedGuest.table,
			address: props.selectedGuest.address,
			amount_guest: guestModel.value.amount_guest,
			level: props.selectedGuest.level
		}
		if (props.agenda.greeting_screen) {
			sendMessage(guestMessage)
		}
		await createItems({ collection: "guest", items: entrustModel.entrusts })
		// console.log("result2", result2)

		emit("after-submit")
		resetEntrust()
		loadingBar.finish()
		if (props.selectedGuest.presence) {
			notification.warning({
				content: `${props.selectedGuest.name} sudah registrasi sebelumnya`,
				description: `Perhatian`,
				duration: 2000
			})
		} else {
			notification.success({
				content: `${props.selectedGuest.name} dinyatakan hadir`,
				description: `Sukses`,
				duration: 2000
			})
		}
	} catch (e) {
		console.error(e)
		if (e.errors) {
			message.error(e.errors[0].message)
			notification.error({
				content: `${e.errors[0].message}`,
				description: `Error`,
				duration: 2000
			})
		} else {
			message.error("gagal submit")
			notification.error({
				content: `gagal konfirmasi`,
				description: `Error`,
				duration: 2000
			})
		}
		loadingBar.error()
	} finally {
		submitLoading.value = false
	}
}

const linkPrint = computed(() => {
	const title = encodeURIComponent(props.agenda.title)
	const name = encodeURIComponent(props.selectedGuest.name)
	const qrvalue = props.selectedGuest.code_guest

	return `my.bluetoothprint.scheme://https://app.bagimomen.my.id/api/print-guest?name=${name}&title=${title}&qrvalue=${qrvalue}`
})

const linkPrintParam = (title, name) => {
	if (!title || !name) return
	const _title = encodeURIComponent(title)
	const _name = encodeURIComponent(name)
	const _qrvalue = encodeURIComponent(`on-site-${title}`)

	return `my.bluetoothprint.scheme://https://app.bagimomen.my.id/api/print-guest?name=${_name}&title=${_title}&qrvalue=${_qrvalue}`
}

const handleClose = () => {
	emit("close")
	resetEntrust()
}
const handleAfterEnter = () => {
	guestModel.value.amount_guest = props.selectedGuest.amount_guest ?? 0
	guestModel.value.angpao = props.selectedGuest.angpao ?? []
	guestModel.value.label = props.selectedGuest.label
	guestModel.value.amount_souvenir = props.selectedGuest.amount_souvenir ?? 0
	guestModel.value.souvenir = props.selectedGuest.take_souvenir ?? false
	guestModel.value.gift = props.selectedGuest.gift ?? []
	guestModel.value.selfie = props.selectedGuest.selfie
}

const printContent = async () => {
	const guestPrint = {
		...props.selectedGuest,
		...guestModel.value
	}
	emit("print-content", guestPrint)
}

const printEntrust = index => {
	emit("print-entrust", entrustModel.entrusts[index])
}
</script>

<style lang="scss" scoped>
::v-deep(.n-input__suffix) {
	gap: 8px;
}
</style>
