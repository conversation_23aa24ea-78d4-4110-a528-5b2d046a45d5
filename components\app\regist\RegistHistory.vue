<template>
	<n-flex vertical>
		<ResultError v-if="fetchError" @refresh="refresh" />
		<template v-else>
			<!-- search -->
			<n-space align="center" :size="viewport.isLessThan('tablet') ? 'small' : 'medium'">
				<n-input
					v-model:value="fetchParam.search"
					placeholder="Cari Tamu"
					clearable
					@keyup.enter="fetchGuests"
					@input="delayedSearch"
					@clear="handleClear"
				/>
				<n-button circle type="primary" :ghost="!isFilterOn" @click="toggleFilter">
					<template #icon>
						<Icon :name="FilterIcon" />
					</template>
				</n-button>
				<n-button circle type="primary" ghost @click="showOnSiteForm = true">
					<template #icon>
						<Icon :name="OnsiteIcon" />
					</template>
				</n-button>
				<n-button circle type="primary" ghost @click="showGiftForm = true">
					<template #icon>
						<Icon :name="GiftIcon" />
					</template>
				</n-button>
				<n-button circle type="primary" :loading="reloadLoading" ghost @click="handleReload">
					<template #icon>
						<Icon :name="ReloadIcon" />
					</template>
				</n-button>
			</n-space>

			<!-- filter -->
			<n-collapse-transition :show="isFilterOn">
				<n-card>
					<template #header>
						Tamu Undangan
						<small
							class="ml-1 mr-2 px-1 py-1 text-[--primary-color] border-[--primary-color] border rounded-md bg-primary bg-[--primary-010-color]"
						>
							{{ guestsMeta.filter_count }}
						</small>
						Pax
						<small
							class="ml-1 px-1 py-1 text-[--primary-color] border-[--primary-color] border rounded-md bg-primary bg-[--primary-010-color]"
						>
							{{ totalAmountGuests }}
						</small>
					</template>

					<n-space vertical>
						<n-flex align="center">
							<span>Level Tamu:</span>
							<n-popselect
								v-model:value="filterLevel"
								:options="optionsFilterLevel"
								@update:value="handleFilter"
							>
								<n-button>{{ filterLevel }}</n-button>
							</n-popselect>
						</n-flex>
					</n-space>
				</n-card>
			</n-collapse-transition>

			<!-- skeleton -->
			<SkeletonList v-if="!guestsData" />

			<template v-if="guestsData">
				<!-- empty -->
				<EmptyData v-if="guestsData.length == 0" @refresh="fetchGuests" />

				<n-flex>
					<!-- @select="handleSelect" -->
					<!-- guests mobile -->
					<RegistGuestsHistoryMobile v-if="viewport.isLessThan('desktop')" :guests="guestsData" />
					<!-- guest desktop -->
					<RegistTableHistory v-else :guests="guestsData" />
				</n-flex>

				<!-- pagination -->
				<n-pagination
					v-model:page="fetchParam.page"
					:page-count="totalPages"
					:page-slot="7"
					@update:page="fetchGuests"
				/>
			</template>

			<!-- on site form -->
			<RegistDrawerOnSite
				v-model:show="showOnSiteForm"
				:agenda="agenda"
				@close="showOnSiteForm = !showOnSiteForm"
				@after-submit="handleAfterSubmit"
				@print-content="printContent"
				@print-entrust="printEntrust"
			/>

			<!-- gift form -->
			<RegistDrawerGift
				v-model:show="showGiftForm"
				:agenda="agenda"
				@close="showGiftForm = !showGiftForm"
				@after-submit="handleAfterSubmit"
				@print-entrust="printEntrust"
			/>
		</template>
	</n-flex>
</template>

<script setup>
import {
	NFlex,
	NSpace,
	NInput,
	NButton,
	NPagination,
	NCollapseTransition,
	NPopselect,
	NCard,
	useLoadingBar
} from "naive-ui"
import { useMainStore } from "@/stores/main"
import _debounce from "lodash/debounce"
import { aggregate, withToken } from "@directus/sdk"

const props = defineProps({
	agenda: Object
})

const emit = defineEmits(["print-content", "print-entrust"])

const printContent = guest => {
	emit("print-content", guest)
}

const printEntrust = guest => {
	emit("print-entrust", guest)
}

const FilterIcon = "ion:filter"
const OnsiteIcon = "mdi:account-plus-outline"
const GiftIcon = "carbon:gift"
const ReloadIcon = "mdi:reload"

const { $directus } = useNuxtApp()
const viewport = useViewport()
const mainStore = useMainStore()
const loadingBar = useLoadingBar()

const isFilterOn = ref(false)

const filterLevel = ref("Semua")
const optionsFilterLevel = [
	{ label: "Semua", value: "Semua" },
	{ label: "Reguler", value: "Reguler" },
	{ label: "VIP", value: "VIP" },
	{ label: "VVIP", value: "VVIP" }
]
const filterAbsent = ref("Semua")

const fetchError = ref(null)
const totalAmountGuests = ref(0)
const showForm = ref(false)
const showOnSiteForm = ref(false)
const showGiftForm = ref(false)
const reloadLoading = ref(false)

const fetchParam = ref({
	filter: {
		event: {
			_eq: props.agenda.id
		},
		presence: {
			_eq: true
		}
	},
	sort: ["-date_updated", "-attendance_time"],
	search: "",
	fields: ["*", "regist_by.first_name", "regist_by.last_name"],
	meta: "*",
	limit: 5,
	page: 1
})

const { data: guestsData, meta: guestsMeta, load } = useGetGuests()
const fetchGuests = async () => {
	loadingBar.start()
	try {
		const filter = {
			event: {
				_eq: props.agenda.id
			},
			presence: {
				_eq: true
			}
		}
		if (isFilterOn.value) {
			if (filterLevel.value !== "Semua") {
				let paramLevel = 0
				switch (filterLevel.value) {
					case "Reguler":
						paramLevel = 1
						break
					case "VIP":
						paramLevel = 2
						break
					case "VVIP":
						paramLevel = 3
						break
					default:
						paramLevel = 1
						break
				}
				filter.level = {
					_eq: paramLevel
				}
			}
			if (filterAbsent.value !== "Semua") {
				let paramPresence = false
				switch (filterAbsent.value) {
					case "Absen":
						paramPresence = false
						break
					case "Hadir":
						paramPresence = true
						break
					default:
						paramPresence = false
						break
				}
				filter.presence = {
					_eq: paramPresence
				}
			}

			const { token } = useDirectusToken()
			const result = await $directus.request(
				withToken(
					token.value,
					aggregate("guest", {
						aggregate: { sum: "amount_guest" },
						query: {
							fields: ["amount_guest"],
							filter
						}
					})
				)
			)
			totalAmountGuests.value = result[0].sum.amount_guest || 0
		}
		const params = {
			...fetchParam.value,
			filter
		}
		await load(params)
	} catch (e) {
		console.error(e)
	} finally {
		loadingBar.finish()
	}
}

const totalPages = computed(() => {
	return Math.ceil(guestsMeta.value?.filter_count / fetchParam.value.limit)
})

const handleFilter = async () => {
	fetchParam.value.page = 1
	await fetchGuests()
}

const handleClear = async () => {
	fetchParam.value.search = ""
	fetchParam.value.page = 1
	await fetchGuests()
}

const delayedSearch = _debounce(async () => {
	fetchParam.value.page = 1
	await fetchGuests()
}, 500)

const refresh = e => {
	mainStore.softReload()
	return e
}

const handleAfterSubmit = async () => {
	await fetchGuests()
	showForm.value = false
	showOnSiteForm.value = false
	showGiftForm.value = false
}

const handleReload = async () => {
	reloadLoading.value = true
	await fetchGuests()
	reloadLoading.value = false
}

onMounted(async () => {
	await fetchGuests()
})
</script>

<style></style>
