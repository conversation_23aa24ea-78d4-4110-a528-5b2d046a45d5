<template>
	<CustomDrawer v-model:show="show" title="Edit Tamu" @close="handleClose" @after-enter="handleAfterEnter">
		<n-form ref="formGuestRef" :rules="rules" :model="guestModel">
			<n-form-item label="Kode (hanya lihat)" path="code_guest">
				<n-input v-model:value="guestModel.code_guest" readonly type="text" placeholder="Masukkan kode tamu" />
			</n-form-item>
			<n-form-item label="Nama" path="name">
				<n-input v-model:value="guestModel.name" type="text" clearable placeholder="Masukkan nama tamu" />
			</n-form-item>
			<n-form-item label="Status/Relasi" path="status_relation">
				<n-input
					v-model:value="guestModel.status_relation"
					type="text"
					clearable
					placeholder="Masukkan status/relasi tamu"
				/>
			</n-form-item>
			<n-grid :span="24" :x-gap="8">
				<n-form-item-gi :span="12" label="Level" path="level">
					<n-select v-model:value="guestModel.level" :options="optionsLevel" />
				</n-form-item-gi>
				<n-form-item-gi :span="12" label="Sesi" path="shift">
					<n-input v-model:value="guestModel.shift" type="text" clearable placeholder="Masukkan sesi tamu" />
				</n-form-item-gi>
			</n-grid>
			<n-grid :span="24" :x-gap="8">
				<n-form-item-gi :span="12" label="Meja" path="table">
					<n-input
						v-model:value="guestModel.table"
						type="text"
						clearable
						placeholder="Masukkan nama meja tamu"
					/>
				</n-form-item-gi>
				<n-form-item-gi :span="12" label="Kategori" path="category">
					<n-input
						v-model:value="guestModel.category"
						type="text"
						clearable
						placeholder="Masukkan kategori tamu"
					/>
				</n-form-item-gi>
			</n-grid>
			<n-form-item label="Alamat" path="address">
				<n-input v-model:value="guestModel.address" type="text" clearable placeholder="Masukkan alamat tamu" />
			</n-form-item>
			<n-form-item label="No. HP" path="phone">
				<n-input v-model:value="guestModel.phone" type="text" clearable placeholder="Mohon diawali dengan 62" />
			</n-form-item>
			<n-grid :span="24" :x-gap="8">
				<n-form-item-gi :span="12" label="Jumlah tamu" path="amount_guest">
					<n-input-number
						v-model:value="guestModel.amount_guest"
						clearable
						:min="0"
						style="width: 100%"
						placeholder="Masukkan jumlah tamu"
					/>
				</n-form-item-gi>
				<n-form-item-gi :span="12" label="Jumlah souvenir" path="amount_souvenir">
					<n-input-number
						v-model:value="guestModel.amount_souvenir"
						clearable
						:min="0"
						style="width: 100%"
						placeholder="Masukkan jumlah souvenir"
					/>
				</n-form-item-gi>
			</n-grid>
			<n-form-item label="Catatan" path="note">
				<n-input
					v-model:value="guestModel.note"
					type="text"
					clearable
					placeholder="Masukkan catatan mengenai tamu"
				/>
			</n-form-item>
			<n-form-item label="RSVP" path="RSVP">
				<n-select v-model:value="guestModel.rsvp" :options="optionRSVP" />
			</n-form-item>
		</n-form>
		<n-collapse>
			<n-collapse-item title="Extras" name="1">
				<n-space>
					<div class="border-2 border-white bg-white">
						<n-qr-code
							id="qr-code"
							:value="props.selectedGuest.code_guest"
							class="p-0 rounded-none bg-white"
						/>
					</div>

					<n-space vertical>
						<p>
							{{ props.selectedGuest.code_guest }}
						</p>
						<n-button @click="handleDownloadQRCode">Download</n-button>
						<n-button @click="copyToClipboard">Salin link undangan</n-button>
					</n-space>
				</n-space>
			</n-collapse-item>
		</n-collapse>
		<template #footer>
			<n-button block :loading="submitLoading" type="primary" @click="handleSubmit">Simpan</n-button>
		</template>
	</CustomDrawer>
</template>

<script setup>
import {
	NH4,
	NSpace,
	NAvatar,
	NThing,
	NCollapse,
	NCollapseItem,
	NQrCode,
	NGrid,
	NFormItemGi,
	NSelect,
	NForm,
	NFormItem,
	NInputNumber,
	NInput,
	NButton,
	useLoadingBar,
	useMessage
} from "naive-ui"
import CustomDrawer from "@/components/app/CustomDrawer.vue"

const props = defineProps({
	agenda: Object,
	selectedGuest: Object
})

const emit = defineEmits(["close", "after-submit"])

const show = defineModel()

const loadingBar = useLoadingBar()
const message = useMessage()
const { updateItem } = useDirectusItems()

const sourceCopy = ref("")
const { copy } = useClipboard({ sourceCopy })

const formGuestRef = ref(null)

const guestModel = ref({
	code_guest: null,
	name: null,
	status_relation: null,
	level: null,
	shift: null,
	table: null,
	category: null,
	address: null,
	phone: null,
	amount_guest: 0,
	amount_souvenir: 0,
	note: null,
	rsvp: null
})

const optionRSVP = [
	{ label: "Belum Mengisi", value: null },
	{ label: "Tidak bisa berhadir", value: "0" },
	{ label: "Akan Berhadir", value: "1" }
]

const rules = {
	code_guest: {
		required: true,
		message: "kode Tamu tidak boleh kosong",
		trigger: ["input", "blur"]
	},
	name: {
		required: true,
		message: "Nama tidak boleh kosong",
		trigger: ["input", "blur"]
	}
}

const submitLoading = ref(false)

const handleSubmit = e => {
	e.preventDefault()
	formGuestRef.value?.validate(async errors => {
		if (!errors) {
			await submitData()
		} else {
			console.error(errors)
		}
	})
}

const submitData = async () => {
	submitLoading.value = true
	loadingBar.start()
	try {
		const prefix = `${props.agenda?.code_event}-`
		if (!guestModel.value.code_guest.startsWith(prefix)) {
			guestModel.value.code_guest = prefix + guestModel.value.code_guest
		}
		guestModel.value.event = props.agenda.id
		const body = {
			...props.selectedGuest,
			...guestModel.value
		}
		await updateItem({ id: props.selectedGuest.id, collection: "guest", item: body })
		message.success("data dirubah")
		emit("after-submit")
		loadingBar.finish()
	} catch (e) {
		console.error(e)
		message.error(e.message)
		loadingBar.error()
	} finally {
		submitLoading.value = false
	}
}

const handleClose = () => {
	emit("close")
}

const handleAfterEnter = () => {
	guestModel.value.code_guest = props.selectedGuest.code_guest
	guestModel.value.name = props.selectedGuest.name
	guestModel.value.status_relation = props.selectedGuest.status_relation ?? null
	guestModel.value.level = String(props.selectedGuest.level ?? "1")
	guestModel.value.shift = props.selectedGuest.shift ?? null
	guestModel.value.table = props.selectedGuest.table ?? null
	guestModel.value.category = props.selectedGuest.category ?? null
	guestModel.value.address = props.selectedGuest.address ?? null
	guestModel.value.phone = props.selectedGuest.phone ?? null
	guestModel.value.note = props.selectedGuest.note ?? null
	guestModel.value.amount_guest = props.selectedGuest.amount_guest ?? 0
	guestModel.value.amount_souvenir = props.selectedGuest.amount_souvenir ?? 0
	guestModel.value.rsvp = props.selectedGuest.rsvp ?? null
}

const handleDownloadQRCode = () => {
	const canvas = document.querySelector("#qr-code")?.querySelector("canvas")
	if (canvas) {
		const url = canvas.toDataURL()
		const a = document.createElement("a")
		a.download = `${props.selectedGuest.code_guest}_${props.selectedGuest.name}.png`
		a.href = url
		document.body.appendChild(a)
		a.click()
		document.body.removeChild(a)
	}
}

const copyToClipboard = () => {
	sourceCopy.value = `${props.agenda.url_post}/?qr=${props.selectedGuest.code_guest}`
	copy(sourceCopy.value)
	message.success("link tersalin ke clipboard")
}
</script>

<style lang="scss" scoped>
::v-deep(.n-input__suffix) {
	gap: 8px;
}
</style>
